import React, { memo, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  FaBluetooth,
  FaWifi,
  FaQrcode,
  FaCreditCard,
  FaMobileAlt
} from 'react-icons/fa';
import { LuNfc } from "react-icons/lu";

const CardTypePreview = memo(({ cardType, isSelected = false, onClick, className = '' }) => {
  // Memoize expensive calculations
  const cardGradient = useMemo(() => {
    const type = cardType?.type_of_connection?.toLowerCase() || 'default';
    const gradients = {
      bluetooth: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 
      nfc: 'linear-gradient(135deg, #100021 0%, #4f0084 100%)',
      qr: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      wifi: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
      default: 'linear-gradient(135deg, #00c3ac 0%, #02aa96 100%)'
    };
    return gradients[type] || gradients.default;
  }, [cardType?.type_of_connection]);

  const connectionIcon = useMemo(() => {
    const iconType = cardType?.type_of_connection?.toLowerCase() || 'default';
    const iconProps = { size: 24, color: 'white' };

    const icons = {
      bluetooth: <FaBluetooth {...iconProps} />,
      nfc: <LuNfc {...iconProps} />,
      qr: <FaQrcode {...iconProps} />,
      wifi: <FaWifi {...iconProps} />,
      default: <FaCreditCard {...iconProps} />
    };
    return icons[iconType] || icons.default;
  }, [cardType?.type_of_connection]);

  return (
    <motion.div
      className={`card-type-preview-container ${className}`}
      onClick={onClick}
      whileHover={{
        scale: 1.02,
        y: -2,
        transition: { duration: 0.2, ease: "easeOut" }
      }}
      whileTap={{
        scale: 0.98,
        transition: { duration: 0.1, ease: "easeOut" }
      }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      role="button"
      tabIndex={0}
      aria-label={`Card type: ${cardType?.name || 'Unnamed'}`}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick && onClick();
        }
      }}
      style={{
        cursor: 'pointer',
        padding: '16px',
        borderRadius: '12px',
        background: 'white',
        boxShadow: isSelected
          ? '0 6px 24px rgba(0, 0, 0, 0.1), 0 0 0 2px #00c3ac'
          : '0 2px 12px rgba(0, 0, 0, 0.06)',
        border: isSelected ? '2px solid #00c3ac' : '2px solid transparent',
        transition: 'all 0.3s ease',
        minHeight: '140px',
        maxHeight: '160px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}
    >
      {/* Card Visual Preview - Compact */}
      <div
        className="card-type-visual"
        style={{
          width: '80px',
          height: '50px',
          background: cardGradient,
          borderRadius: '8px',
          position: 'relative',
          overflow: 'hidden',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: '12px'
        }}
      >
        {/* Card content overlay */}
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(255, 255, 255, 0.1)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white'
          }}
        >
          {/* Connection type icon */}
          {connectionIcon}
        </div>

        {/* Decorative elements */}
        <div
          style={{
            position: 'absolute',
            top: '4px',
            right: '4px',
            width: '8px',
            height: '8px',
            borderRadius: '50%',
            background: 'rgba(255, 255, 255, 0.2)',
            border: '1px solid rgba(255, 255, 255, 0.3)'
          }}
        />
        <div
          style={{
            position: 'absolute',
            bottom: '4px',
            left: '4px',
            width: '6px',
            height: '6px',
            borderRadius: '50%',
            background: 'rgba(255, 255, 255, 0.15)',
            border: '1px solid rgba(255, 255, 255, 0.2)'
          }}
        />
      </div>

      {/* Card Type Information */}
      <div style={{ width: '100%', textAlign: 'center' }}>
        {/* Card type name */}
        <h4
          style={{
            fontSize: '14px',
            fontWeight: '600',
            color: '#1f2937',
            margin: '0 0 4px 0',
            lineHeight: '1.3'
          }}
        >
          {cardType?.name || 'Unnamed Type'}
        </h4>
        
        {/* Connection type */}
        <p
          style={{
            fontSize: '12px',
            color: '#6b7280',
            margin: '0',
            textTransform: 'capitalize'
          }}
        >
          {cardType?.type_of_connection || 'Standard'}
        </p>
      </div>
    </motion.div>
  );
});

CardTypePreview.displayName = 'CardTypePreview';

export default CardTypePreview;
