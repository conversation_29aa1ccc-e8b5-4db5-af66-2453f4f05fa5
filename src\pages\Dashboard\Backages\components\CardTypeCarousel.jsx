import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import CardTypePreview from './CardTypePreview';
import './CardTypeCarousel.css';

const CardTypeCarousel = ({ 
  cardTypes = [], 
  onCardTypeSelect, 
  selectedCardType = null,
  itemsPerView = { desktop: 4, tablet: 3, mobile: 2 },
  className = ''
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState(0);
  const carouselRef = useRef(null);

  // Mobile detection
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Calculate items per view based on screen size
  const getItemsPerView = () => {
    if (isMobile) return itemsPerView.mobile;
    if (isTablet) return itemsPerView.tablet;
    return itemsPerView.desktop;
  };

  const currentItemsPerView = getItemsPerView();
  const maxIndex = Math.max(0, cardTypes.length - currentItemsPerView);

  // Navigation functions
  const goToNext = () => {
    setCurrentIndex(prev => Math.min(prev + 1, maxIndex));
  };

  const goToPrevious = () => {
    setCurrentIndex(prev => Math.max(prev - 1, 0));
  };

  const goToSlide = (index) => {
    setCurrentIndex(Math.min(Math.max(index, 0), maxIndex));
  };

  // Touch/drag handlers
  const handleDragStart = (e) => {
    setIsDragging(true);
    setDragStart(e.type === 'mousedown' ? e.clientX : e.touches[0].clientX);
  };

  const handleDragEnd = (e) => {
    if (!isDragging) return;
    
    const dragEnd = e.type === 'mouseup' ? e.clientX : e.changedTouches[0].clientX;
    const dragDistance = dragStart - dragEnd;
    const threshold = 50;

    if (Math.abs(dragDistance) > threshold) {
      if (dragDistance > 0) {
        goToNext();
      } else {
        goToPrevious();
      }
    }

    setIsDragging(false);
    setDragStart(0);
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (document.activeElement?.closest('.card-type-carousel')) {
        if (e.key === 'ArrowLeft') {
          e.preventDefault();
          goToPrevious();
        } else if (e.key === 'ArrowRight') {
          e.preventDefault();
          goToNext();
        } else if (e.key === 'Home') {
          e.preventDefault();
          goToSlide(0);
        } else if (e.key === 'End') {
          e.preventDefault();
          goToSlide(maxIndex);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [maxIndex]);

  // Auto-adjust index when cardTypes change
  useEffect(() => {
    if (currentIndex > maxIndex) {
      setCurrentIndex(maxIndex);
    }
  }, [cardTypes.length, currentItemsPerView, maxIndex]);

  if (!cardTypes || cardTypes.length === 0) {
    return (
      <div className="flex items-center justify-center h-32 text-gray-500 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        <div className="text-center">
          <div className="text-2xl mb-2">📋</div>
          <p className="text-sm">No card types available</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`card-type-carousel ${className}`}
      role="region"
      aria-label="Card types selection carousel"
      tabIndex={0}
    >
      {/* Navigation arrows and indicators */}
      <div className="flex items-center justify-between mb-4">
        <motion.button
          onClick={goToPrevious}
          disabled={currentIndex === 0}
          className="carousel-nav-btn"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          aria-label="Previous card types"
          title="Previous card types"
        >
          <FiChevronLeft size={20} />
        </motion.button>

        {/* Slide indicators */}
        <div className="flex items-center space-x-2" role="tablist" aria-label="Carousel navigation">
          {Array.from({ length: maxIndex + 1 }, (_, index) => (
            <motion.button
              key={index}
              onClick={() => goToSlide(index)}
              className="carousel-indicator"
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.9 }}
              role="tab"
              aria-selected={index === currentIndex}
              aria-label={`Go to slide ${index + 1}`}
              title={`Slide ${index + 1}`}
              style={{
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                border: 'none',
                background: index === currentIndex ? '#00c3ac' : '#d1d5db',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
            />
          ))}
        </div>

        <motion.button
          onClick={goToNext}
          disabled={currentIndex === maxIndex}
          className="carousel-nav-btn"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          aria-label="Next card types"
          title="Next card types"
        >
          <FiChevronRight size={20} />
        </motion.button>
      </div>

      {/* Carousel container */}
      <div
        className="carousel-container"
        ref={carouselRef}
        onMouseDown={handleDragStart}
        onMouseUp={handleDragEnd}
        onTouchStart={handleDragStart}
        onTouchEnd={handleDragEnd}
      >
        <motion.div
          className="carousel-track"
          style={{
            display: 'flex',
            cursor: isDragging ? 'grabbing' : 'grab'
          }}
          animate={{
            x: `-${currentIndex * (100 / currentItemsPerView)}%`
          }}
          transition={{
            type: 'spring',
            stiffness: 300,
            damping: 30
          }}
        >
          {cardTypes.map((cardType, index) => (
            <div
              key={cardType.id || index}
              className="carousel-item"
              style={{
                flex: `0 0 ${100 / currentItemsPerView}%`,
                maxWidth: `${100 / currentItemsPerView}%`
              }}
            >
              <CardTypePreview
                cardType={cardType}
                isSelected={selectedCardType?.id === cardType.id}
                onClick={() => onCardTypeSelect && onCardTypeSelect(cardType)}
              />
            </div>
          ))}
        </motion.div>
      </div>
    </div>
  );
};

export default CardTypeCarousel;
