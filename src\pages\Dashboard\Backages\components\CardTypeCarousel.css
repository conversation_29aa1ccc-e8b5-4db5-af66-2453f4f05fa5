/* Card Type Carousel Styles for Form */
:root {
  --main-color: #00c3ac;
  --main-color-hover: #02aa96;
  --gray: #dcdcdc;
}

.card-type-carousel {
  width: 100%;
  position: relative;
  margin-bottom: 24px;
}

.carousel-container {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  background: transparent;
}

.carousel-track {
  display: flex;
  gap: 16px;
  transition: transform 0.3s ease;
  will-change: transform;
}

.carousel-item {
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  padding: 0 4px;
}

/* Navigation buttons */
.carousel-nav-btn {
  padding: 10px;
  border-radius: 50%;
  border: none;
  background: var(--main-color);
  color: white;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 195, 172, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.carousel-nav-btn:hover:not(:disabled) {
  background: var(--main-color-hover);
  box-shadow: 0 4px 12px rgba(0, 195, 172, 0.4);
  transform: translateY(-1px);
}

.carousel-nav-btn:disabled {
  background: #f3f4f6;
  color: #9ca3af;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.carousel-nav-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 195, 172, 0.3);
}

/* Indicators */
.carousel-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 2px;
}

.carousel-indicator:hover {
  transform: scale(1.2);
}

.carousel-indicator.active {
  transform: scale(1.3);
}

/* Card Type Preview Styles */
.card-type-preview-container {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: transform;
  min-height: 140px;
  max-height: 160px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.card-type-preview-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
}

.card-type-preview-container.selected {
  border: 2px solid var(--main-color);
  box-shadow: 0 6px 24px rgba(0, 195, 172, 0.2);
}

.card-type-visual {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  margin-bottom: 12px;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .card-type-carousel {
    padding: 0 8px;
    margin-bottom: 20px;
  }

  .carousel-track {
    gap: 12px;
  }

  .carousel-nav-btn {
    width: 36px;
    height: 36px;
    padding: 8px;
  }

  .card-type-preview-container {
    padding: 12px;
    border-radius: 10px;
    min-height: 120px;
    max-height: 140px;
  }

  .card-type-visual {
    width: 70px;
    height: 44px;
    margin-bottom: 10px;
  }

  /* Touch-friendly indicators */
  .carousel-indicator {
    width: 10px;
    height: 10px;
    margin: 0 3px;
  }
}

@media (max-width: 480px) {
  .card-type-carousel {
    padding: 0 4px;
    margin-bottom: 16px;
  }

  .carousel-track {
    gap: 10px;
  }

  .card-type-preview-container {
    padding: 10px;
    min-height: 110px;
    max-height: 130px;
  }

  .card-type-visual {
    width: 60px;
    height: 38px;
    margin-bottom: 8px;
  }

  .carousel-nav-btn {
    width: 32px;
    height: 32px;
    padding: 6px;
  }
}

/* Tablet Styles */
@media (min-width: 769px) and (max-width: 1024px) {
  .carousel-track {
    gap: 14px;
  }

  .card-type-preview-container {
    padding: 14px;
    min-height: 130px;
    max-height: 150px;
  }

  .card-type-visual {
    width: 75px;
    height: 47px;
  }
}

/* Desktop Styles */
@media (min-width: 1025px) {
  .carousel-track {
    gap: 16px;
  }

  .card-type-preview-container {
    padding: 16px;
    min-height: 140px;
    max-height: 160px;
  }

  .card-type-visual {
    width: 80px;
    height: 50px;
  }

  .carousel-nav-btn {
    width: 40px;
    height: 40px;
  }
}

/* Loading Animation */
.carousel-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 160px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid var(--main-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Accessibility */
.carousel-nav-btn:focus,
.carousel-indicator:focus,
.card-type-preview-container:focus {
  outline: 2px solid var(--main-color);
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .carousel-track,
  .card-type-preview-container,
  .carousel-nav-btn,
  .carousel-indicator {
    transition: none;
    animation: none;
  }
}
